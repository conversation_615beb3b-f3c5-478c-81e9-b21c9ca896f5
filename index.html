<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>24小时时间管理</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: #f5f5f5; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); display: flex; flex-direction: column; }

        .header { text-align: center; padding: 20px; background: #f8f9fa; border-bottom: 1px solid #ddd; }
        .header h1 { font-size: 24px; margin-bottom: 15px; color: #333; }
        .date-controls { display: flex; justify-content: center; align-items: center; gap: 20px; margin-bottom: 15px; }
        .nav-btn { background: none; border: 1px solid #ccc; padding: 8px 15px; cursor: pointer; font-size: 16px; border-radius: 4px; transition: background-color 0.2s; }
        .nav-btn:hover:not(:disabled) { background: #f0f0f0; }
        .nav-btn:disabled { opacity: 0.4; cursor: not-allowed; }
        .current-date { font-size: 18px; font-weight: bold; color: #333; min-width: 120px; transition: all 0.2s; }
        .current-date.is-today { border: 2px solid #A52A2A; padding: 3px 6px; border-radius: 4px; }
        .right-buttons { display: flex; flex-direction: row-reverse; gap: 10px; }
        .btn { background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 14px; transition: background-color 0.2s; }
        .btn:hover { background: #5a6268; }
        .score-btn { /* Specific class for score buttons */ }
        .score-complete { background: #007bff; } .score-complete:hover { background: #0056b3; }
        .score-half { background: #A0C4FF; color: #212529; } .score-half:hover { background: #8ab4f8; }
        .score-btn.inactive { background: white; border: 1px solid #ccc; color: #333; }
        .score-btn.inactive:hover { background: #f0f0f0; }
        .btn:disabled, .score-btn.hidden { opacity: 0.5; cursor: not-allowed; }
        .score-btn.hidden { display: none !important; }

        .color-panel-section {display:inline-block;position:sticky; top:0px; z-index: 100; padding: 10px 15px; border-bottom: 1px solid #ddd; background-color: #f8f9fa; }
        .color-panel { display: flex; flex-direction: column; }
        .color-panel-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;}
        .color-panel-header h3 { font-size: 15px; margin: 0; }
        #toggleCustomizationBtn { font-size: 12px; padding: 3px 8px; opacity: 0; transition: opacity 0.3s ease; }
        .color-panel-header:hover #toggleCustomizationBtn { opacity: 1; }

        #colorSwatchesContainer { display: flex; flex-wrap: wrap; gap: 8px; overflow-x: auto; }
        .color-swatch-item { display: flex; flex-direction: column; align-items: center; padding: 6px; border: 1px solid #eee; border-radius: 4px; cursor: pointer; background-color: white; min-width: 65px; text-align: center; }
        .color-swatch-item.selected { border-color: #007bff; box-shadow: 0 0 5px rgba(0,123,255,0.5); }
        .swatch-color-display { width: 28px; height: 28px; border-radius: 4px; margin-bottom: 5px; border: 1px solid #ccc; }
        .swatch-activity-name { font-size: 11px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 60px; }

        .color-customization-form { margin-top: 10px; padding-top: 10px; border-top: 1px solid #ddd; display: none; }
        .color-customization-form.visible { display: block; }
        .color-customization-form h4 { font-size: 14px; margin-bottom: 8px;}
        .color-customization-form label { font-size: 12px; display: block; margin-bottom: 3px;}
        .color-customization-form input[type="color"],
        .color-customization-form input[type="text"] { width: calc(50% - 5px); padding: 6px; margin-bottom: 8px; border: 1px solid #ccc; border-radius: 3px; font-size: 13px; display: inline-block; }
        .color-customization-form input[type="text"] { margin-left: 5px; }
        .color-customization-form .form-actions { display: flex; gap: 10px; margin-top: 5px; }
        .color-customization-form .form-actions .btn { flex-grow: 1; padding: 6px 10px; font-size: 13px; }

        .main-content { display: flex; height: 650px; }
        .time-section { flex: 2; border-right: 1px solid #ddd; overflow-y: auto; position: relative; }
        
        .daily-note-slot { border-top: 2px solid #aaa !important; background-color: #fdfdfd; margin-top: 10px; }
        .daily-note-label { width: 83px; margin-right: 8px; font-weight: bold; font-size: 14px; color: #495057; flex-shrink: 0; display: flex; align-items: center; justify-content: center; }
        
        .time-period-separator { padding: 5px 15px; background-color: #e9ecef; color: #495057; font-size: 12px; font-weight: bold; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; }
        .time-period-separator:first-child { border-top: none; }
        .time-slot { display: flex; align-items: stretch; padding: 8px 10px; border-bottom: 1px solid #e9ecef; min-height: 50px; position: relative; }
        .color-strip { width: 15px; margin-right: 8px; background-color: white; border: 1px solid #eee; border-radius: 3px; cursor: pointer; flex-shrink: 0; }
        .time-label { width: 60px; font-weight: bold; font-size: 14px; flex-shrink: 0; cursor: pointer; padding: 3px; border-radius: 3px; text-align: center; display:flex; align-items:center; justify-content:center; margin-right: 8px; }
        .time-label:hover { background-color: #e9ecef; }
        .time-slot.current-time .time-label { background-color: #007bff; color: white; font-weight: bold; }
        .task-input { flex: 1; border: none; background: transparent; padding: 5px 0; font-size: 14px; outline: none; color: #333; align-self: stretch;}
        .task-input.completed { text-decoration: line-through; color: #6c757d; }
        .task-checkbox { margin-left: 10px; width: 18px; height: 18px; cursor: pointer; align-self: center; }
        .notes-section { flex: 1; display: flex; flex-direction: column; }
        .note-box { flex: 1; padding: 20px; border-bottom: 1px solid #ddd; display: flex; flex-direction: column; }
        .note-box:last-child { border-bottom: none; }
        .note-box h3 { margin-bottom: 10px; color: #333; font-size: 16px; cursor: text; padding: 2px 4px; border-radius: 3px; }
        .note-box h3:hover, .note-box h3:focus { background-color: #f0f0f0; outline: 1px solid #ccc; }
        .note-textarea {min-height: 180px; width: 100%; flex-grow: 1; border: 1px solid #ddd; padding: 10px; resize: none; font-family: inherit; font-size: 14px; outline: none; }
        .note-textarea:focus { border-color: #007bff; }

        .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); justify-content: center; align-items: center; }
        .modal-content { background-color: white; padding: 0; border-radius: 8px; width: 500px; max-width: 90%; box-shadow: 0 5px 15px rgba(0,0,0,0.3); display: flex; flex-direction: column; max-height: 80vh;}
        .modal-header { display: flex; justify-content: space-between; align-items: center; padding: 15px 20px; border-bottom: 1px solid #eee; }
        .modal-header h2 { font-size: 18px; margin: 0; }
        .close { font-size: 28px; cursor: pointer; color: #999; font-weight: bold; } .close:hover { color: #333; }
        .modal-body { padding: 20px; overflow-y: auto; }
        .modal-footer { padding: 15px 20px; border-top: 1px solid #eee; text-align: right; }
        .modal-footer .btn { margin-left: 10px; }
        
        #calendarModal .modal-content { width: 650px; }
        .calendar { padding: 20px; }
        .calendar-nav { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .calendar-nav button { background: none; border: 1px solid #ccc; color: #333; padding: 8px 12px; border-radius: 4px; cursor: pointer; }
        .calendar-nav button:hover { background: #f0f0f0; }
        .calendar-nav #calendarTodayBtn { background-color: #007bff; color: white; border-color: #007bff; }
        .calendar-nav #calendarTodayBtn:hover { background-color: #0056b3; }
        .calendar-grid { display: grid; grid-template-columns: repeat(7, 1fr); gap: 5px; }
        .calendar-day { padding: 8px; text-align: center; cursor: pointer; border-radius: 4px; border: 1px solid #eee; position: relative; display: flex; flex-direction: column; justify-content: flex-start; align-items: center; min-height: 85px; }
        .calendar-day:hover { background: #f0f0f0; }
        .calendar-day.today { background-color: #A52A2A; color: white; border-color: #A52A2A; }
        .calendar-day.selected { background: none; border: 2px solid #007bff; color: #212529; } 
        .calendar-day.other-month { color: #ccc; }
        .day-number-wrapper { position: relative; width: 100%; text-align: center; font-size: 16px; font-weight: bold; }
        .day-score { position: absolute; top: 0; right: 2px; font-size: 10px; background: rgba(255,255,255,0.8); color: #333; padding: 1px 3px; border-radius: 2px; }
        .day-note { font-size: 11px; line-height: 1.3; color: #555; text-align: center; word-break: break-all; max-height: 2.6em; overflow: hidden; margin-top: auto; align-self: stretch; }
        .calendar-day.today .day-note { color: white; opacity: 0.9; }
        
        .context-menu { display: none; position: absolute; background-color: white; border: 1px solid #ddd; box-shadow: 0 2px 5px rgba(0,0,0,0.15); border-radius: 4px; z-index: 100; padding: 5px 0; min-width: 180px; }
        .context-menu ul { list-style: none; margin: 0; padding: 0; }
        .context-menu li { padding: 8px 15px; cursor: pointer; font-size: 14px; }
        .context-menu li:hover { background-color: #f0f0f0; }
        
        #arrangeTaskModal .modal-content { width: 600px; }
        .task-list-container { margin-top: 10px; }
        .task-list-container h3 { font-size: 16px; margin-bottom: 10px; }
        .task-category-group { margin-bottom: 15px; }
        .task-category-group h4 { font-size: 14px; color: #555; margin-bottom: 8px; padding-bottom: 5px; border-bottom: 1px dotted #ccc; }
        .task-item { display: flex; flex-direction: column; padding: 8px 5px; border-bottom: 1px solid #f0f0f0; font-size: 14px; }
        .task-item:last-child { border-bottom: none; }
        .task-item-main { display:flex; justify-content: space-between; align-items: center; width:100%;}
        .task-item-details { flex-grow: 1; }
        .task-item .is-recurring-badge { font-size: 12px; color: #28a745; font-weight: bold; margin-left: 5px; }
        .task-item-actions-toggle button { margin-left: 5px; padding: 4px 8px; font-size: 13px; cursor: pointer; background-color: #6c757d; color:white; border:none; border-radius:3px;}
        .task-item-hidden-actions { display: none; margin-top: 8px; padding: 8px; background-color: #f9f9f9; border-radius: 4px; }
        .task-item-hidden-actions.visible { display: flex; flex-direction: column; gap: 8px; }
        .task-item-hidden-actions button { padding: 5px 10px; font-size: 13px; cursor: pointer; border-radius:3px; border:none; }
        .task-item-hidden-actions .btn-edit-task { background-color: #007bff; color:white; }
        .task-item-hidden-actions .btn-toggle-recurring { background-color: #ffc107; color:#333; }
        .task-item-hidden-actions .btn-delete-task { background-color: #dc3545; color:white; }
        .edit-task-form { margin-top: 8px; padding: 10px; background-color: #f0f0f0; border-radius: 4px; }
        .edit-task-form label { display: block; margin-top: 5px; font-size: 13px; }
        .edit-task-form input[type="text"] { width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; font-size: 14px; }
        .edit-task-form input[type="checkbox"] { margin-right: 5px; vertical-align: middle; }
        .edit-task-form .edit-actions { margin-top:10px; display:flex; gap:10px; }
        .edit-task-form .edit-actions button { padding: 6px 12px; }
        .nav { display: flex; gap:10px; align-items: center; }

        @media (max-width: 768px) {
            body { padding: 0; }
            .container { border-radius: 0; }
            .main-content { flex-direction: column; height: auto; }
            #colorSwatchesContainer { flex-wrap: nowrap !important;}
            .color-swatch-item { min-width: 35px; padding: 3px; overflow: hidden; }
            .swatch-color-display { width: 20px; height: 20px; }
            .swatch-activity-name { font-size:8px}
            .color-strip { width:25px;}
            .time-label {width: 50px}
            .time-section { border-right: none; border-bottom: 1px solid #ddd; max-height: none; }
            .notes-section { flex-direction: row; }
            .note-box { border-bottom: none; border-right: 1px solid #ddd; min-height: 150px; }
            .note-box:last-child { border-right: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sticky-top">
            <div class="header">
                <h1>24小时时间管理</h1>
                <div class="date-controls">
                    <div class="nav">
                        <button class="nav-btn" id="prevDate">&lt;</button>
                        <span class="current-date" id="currentDate"></span>
                        <button class="nav-btn" id="nextDate">&gt;</button>
                    </div>
                </div>
                <div class="right-buttons">
                    <button class="btn" id="calendarBtn">日历</button>
                    <button class="btn score-btn score-complete" id="completeBtn">完成</button>
                    <button class="btn score-btn score-half" id="halfBtn">半</button>
                </div>
            </div>
        </div>
        <div class="color-panel-section">
            <div class="color-panel" id="colorPanel">
                <div class="color-panel-header">
                    <h3>颜色标签</h3>
                    <button id="toggleCustomizationBtn" class="btn" style="background-color: #6c757d;">自定义</button>
                </div>
                <div id="colorSwatchesContainer"></div>
                <div class="color-customization-form" id="colorCustomizationForm">
                    <h4>自定义颜色标签</h4>
                    <label for="customColorPicker">颜色:</label>
                    <input type="color" id="customColorPicker">
                    <label for="customActivityName" style="margin-left:5px;">活动名称:</label>
                    <input type="text" id="customActivityName" placeholder="例如: 学习">
                    <div class="form-actions">
                        <button id="updateColorBtn" class="btn">保存更改</button>
                        <button id="addNewColorBtn" class="btn" style="background-color:#17a2b8;">作为新建</button>
                        <button id="deleteColorBtn" class="btn" style="background-color:#dc3545;">删除选中</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="main-content">
            <div class="time-section" id="timeSection">
                <div id="timeLabelContextMenu" class="context-menu">
                    <ul>
                        <li data-action="clear">清空当前条</li>
                        <li data-action="split">分割任务 ( | )</li>
                        <li data-action="collect">收纳到任务列表</li>
                        <li data-action="arrange">从列表安排任务</li>
                    </ul>
                </div>
            </div>
            
            <div class="notes-section">
                <div class="note-box"><h3 contenteditable="true">文本框1</h3><textarea class="note-textarea" id="note1"></textarea></div>
                <div class="note-box"><h3 contenteditable="true">文本框2</h3><textarea class="note-textarea" id="note2"></textarea></div>
            </div>
        </div>

        <div id="calendarModal" class="modal"><div class="modal-content">
            <div class="modal-header"><h2>选择日期</h2><span class="close" data-modal-id="calendarModal">&times;</span></div>
            <div id="calendar" class="calendar"></div>
        </div></div>

        <div id="arrangeTaskModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>从列表选择任务 / 管理</h2>
                    <span class="close" data-modal-id="arrangeTaskModal">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="task-list-container" id="taskListInModal">
                        <h3>任务列表</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class TodoManager {
            constructor() {
                this.currentDate = new Date();
                this.currentDate.setHours(0,0,0,0);
                this.data = this.loadData();
                this.currentTimeInterval = null;
                this.contextMenuHour = null;
                this.calendarViewDate = new Date(this.currentDate);
                
                this.selectedColorIndex = 0; 
                this.isPaintingColor = false; 

                if (!this.data.colorPalette || !Array.isArray(this.data.colorPalette) || this.data.colorPalette.length < 1) {
                    this.data.colorPalette = [ 
                        { color: '#FFFFFF', activity: '清除' }, { color: '#FFADAD', activity: '睡眠' },
                        { color: '#FFD6A5', activity: '工作' }, { color: '#FDFFB6', activity: '学习' },
                        { color: '#CAFFBF', activity: '运动' }, { color: '#9BF6FF', activity: '休闲' },
                        { color: '#A0C4FF', activity: '用餐' }, { color: '#BDB2FF', activity: '交通' },
                        { color: '#FFC6FF', activity: '其他' }
                    ];
                }
                this.editingTaskIndex = null; 
                this.init();
            }

            init() {
                this.renderColorPanel(); 
                this.createTimeSlots();
                this.bindEvents();
                this.updateDisplay();
                this.loadPageData();
                this.startCurrentTimeUpdate();
                this.loadGlobalNotes();
                document.getElementById('timeLabelContextMenu').style.display = 'none'; 
                
                this.updateNavButtonStates();
                setTimeout(() => this.scrollToCurrentTime(), 100);
            }

            updateNavButtonStates() {
                const today = new Date(); today.setHours(0,0,0,0);
                const yesterday = new Date(today); yesterday.setDate(today.getDate() - 1);
                const tomorrow = new Date(today); tomorrow.setDate(today.getDate() + 1);

                const current = this.currentDate.getTime();
                
                document.getElementById('prevDate').disabled = current <= yesterday.getTime();
                document.getElementById('nextDate').disabled = current >= tomorrow.getTime();
            }

            changeDateAndReload(delta) { 
                this.currentDate.setDate(this.currentDate.getDate() + delta);
                this.calendarViewDate = new Date(this.currentDate); 
                this.updateDisplay();
                this.loadPageData();
                this.updateCurrentTime();
                this.updateNavButtonStates();
                this.hideContextMenu();
            }

            scrollToCurrentTime() {
                const today = new Date(); today.setHours(0, 0, 0, 0);
                if (this.currentDate.getTime() !== today.getTime()) {
                    return;
                }
                const currentSlot = document.querySelector('.time-slot.current-time');
                if (currentSlot) {
                    const timeSection = document.getElementById('timeSection');
                    const elementRect = currentSlot.getBoundingClientRect();
                    const parentRect = timeSection.getBoundingClientRect();
                    const offset = elementRect.top - parentRect.top - (parentRect.height / 2) + (elementRect.height / 2);

                    timeSection.scrollTo({
                        top: timeSection.scrollTop + offset,
                        behavior: 'smooth'
                    });
                }
            }
            
            renderColorPanel() {
                const swatchesContainer = document.getElementById('colorSwatchesContainer');
                swatchesContainer.innerHTML = ''; 
                this.data.colorPalette.forEach((item, index) => {
                    const swatchItem = document.createElement('div');
                    swatchItem.className = 'color-swatch-item';
                    if (index === this.selectedColorIndex) {
                        swatchItem.classList.add('selected');
                    }
                    swatchItem.dataset.index = index;
                    swatchItem.innerHTML = `
                        <div class="swatch-color-display" style="background-color: ${item.color};"></div>
                        <span class="swatch-activity-name">${item.activity}</span>
                    `;
                    swatchItem.addEventListener('click', () => this.selectColorFromPanel(index));
                    swatchesContainer.appendChild(swatchItem);
                });

                const customColorPicker = document.getElementById('customColorPicker');
                const customActivityName = document.getElementById('customActivityName');
                const updateBtn = document.getElementById('updateColorBtn');

                if (this.selectedColorIndex !== -1 && this.selectedColorIndex < this.data.colorPalette.length) {
                    const selected = this.data.colorPalette[this.selectedColorIndex];
                    customColorPicker.value = selected.color;
                    customActivityName.value = selected.activity;
                    customColorPicker.disabled = false;
                    customActivityName.disabled = false;
                    updateBtn.disabled = false;
                } else {
                    customColorPicker.value = '#ffffff'; 
                    customActivityName.value = '';
                    customColorPicker.disabled = true;
                    customActivityName.disabled = true;
                    updateBtn.disabled = true;
                }
            }

            selectColorFromPanel(index) {
                this.selectedColorIndex = index;
                this.renderColorPanel(); 
            }

            updateSelectedColor() {
                if (this.selectedColorIndex === -1 || this.selectedColorIndex >= this.data.colorPalette.length) {
                     alert("请先选择一个颜色进行修改。"); return;
                }
                const newColor = document.getElementById('customColorPicker').value;
                const newActivity = document.getElementById('customActivityName').value.trim() || "未命名";
                this.data.colorPalette[this.selectedColorIndex] = { color: newColor, activity: newActivity };
                this.saveData();
                this.renderColorPanel(); 
                this.loadPageData(); 
            }
            
            addNewColor() {
                const newColor = document.getElementById('customColorPicker').value;
                const newActivity = document.getElementById('customActivityName').value.trim();
                if (!newActivity) {
                    alert("活动名称不能为空。");
                    return;
                }
                this.data.colorPalette.push({ color: newColor, activity: newActivity });
                this.selectedColorIndex = this.data.colorPalette.length - 1;
                this.saveData();
                this.renderColorPanel();
                this.loadPageData();
            }

            deleteSelectedColor() {
                if (this.selectedColorIndex <= 0) {
                    alert("无法删除'清除'标签。");
                    return;
                }
                if (this.selectedColorIndex === -1 || this.selectedColorIndex >= this.data.colorPalette.length) {
                    alert("没有选中任何颜色。");
                    return;
                }
                const colorToDelete = this.data.colorPalette[this.selectedColorIndex];
                if (confirm(`确定要删除 "${colorToDelete.activity}" 标签吗?`)) {
                    this.data.colorPalette.splice(this.selectedColorIndex, 1);
                    this.selectedColorIndex = 0;
                    this.saveData();
                    this.renderColorPanel();
                    this.loadPageData(); 
                }
            }

            createTimeSlots() {
                const timeSection = document.getElementById('timeSection');
                Array.from(timeSection.children).forEach(child => {
                    if (!child.classList.contains('context-menu')) {
                        timeSection.removeChild(child);
                    }
                });
                
                const periodNames = { 0: '凌晨', 6: '上午', 12: '下午', 18: '晚上' };
                for (let hour = 0; hour < 24; hour++) {
                    if (periodNames[hour] !== undefined) {
                        const sep = document.createElement('div');
                        sep.className = 'time-period-separator'; sep.textContent = periodNames[hour];
                        timeSection.appendChild(sep);
                    }
                    const slot = document.createElement('div');
                    slot.className = 'time-slot'; slot.dataset.hour = hour;
                    slot.innerHTML = `
                        <div class="color-strip" data-hour="${hour}"></div> 
                        <div class="time-label" data-hour="${hour}">${hour.toString().padStart(2, '0')}:00</div>
                        <input type="text" class="task-input" data-hour="${hour}">
                        <input type="checkbox" class="task-checkbox" data-hour="${hour}">`;
                    timeSection.appendChild(slot);
                }
                
                const noteSlot = document.createElement('div');
                noteSlot.className = 'time-slot daily-note-slot';
                noteSlot.innerHTML = `
                    <div class="daily-note-label">便条</div>
                    <input type="text" class="task-input" id="dailyNoteInput" placeholder="记录今日摘要，会显示在日历上...">
                `;
                timeSection.appendChild(noteSlot);
            }

            startCurrentTimeUpdate() { 
                this.updateCurrentTime();
                if (this.currentTimeInterval) clearInterval(this.currentTimeInterval);
                this.currentTimeInterval = setInterval(() => this.updateCurrentTime(), 60000);
            }
            updateCurrentTime() {
                const now = new Date();
                const currentHour = now.getHours();
                const todayStr = this.formatDate(now);
                const selectedDateStr = this.formatDate(this.currentDate);
                document.querySelectorAll('.time-slot').forEach(slot => {
                    slot.classList.remove('current-time');
                });
                if (todayStr === selectedDateStr) {
                    const currentSlot = document.querySelector(`.time-slot[data-hour="${currentHour}"]`);
                    if (currentSlot) {
                        currentSlot.classList.add('current-time');
                    }
                }
            }

            bindEvents() {
                document.getElementById('prevDate').addEventListener('click', () => this.changeDateAndReload(-1));
                document.getElementById('nextDate').addEventListener('click', () => this.changeDateAndReload(1));
                document.getElementById('calendarBtn').addEventListener('click', () => { this.showCalendar(); this.hideContextMenu(); });
                document.getElementById('completeBtn').addEventListener('click', () => this.setDayScore('complete'));
                document.getElementById('halfBtn').addEventListener('click', () => this.setDayScore('half'));
                
                document.getElementById('toggleCustomizationBtn').addEventListener('click', () => {
                    document.getElementById('colorCustomizationForm').classList.toggle('visible');
                });
                document.getElementById('updateColorBtn').addEventListener('click', () => this.updateSelectedColor());
                document.getElementById('addNewColorBtn').addEventListener('click', () => this.addNewColor());
                document.getElementById('deleteColorBtn').addEventListener('click', () => this.deleteSelectedColor());

                const timeSection = document.getElementById('timeSection');
                
                timeSection.addEventListener('input', e => {
                    if (e.target.classList.contains('task-input')) {
                        if (e.target.id === 'dailyNoteInput') {
                            this.saveDailyNote();
                        } else {
                            this.saveTask(e.target.dataset.hour, e.target.value, null, null);
                        }
                    }
                });
                timeSection.addEventListener('change', e => {
                    if (e.target.classList.contains('task-checkbox')) {
                        const hour = e.target.dataset.hour;
                        const taskInput = document.querySelector(`.task-input[data-hour="${hour}"]`);
                        taskInput.classList.toggle('completed', e.target.checked);
                        this.saveTask(hour, null, e.target.checked, null);
                    }
                });
                timeSection.addEventListener('click', e => {
                    if (e.target.classList.contains('time-label')) {
                        this.contextMenuHour = parseInt(e.target.dataset.hour);
                        this.showContextMenu(e.target);
                    } else if (e.target.classList.contains('color-strip')) { 
                        this.handleColorStripClick(e.target);
                    } else if (!e.target.closest('.context-menu')) {
                        this.hideContextMenu();
                    }
                });
                
                timeSection.addEventListener('mousedown', e => {
                    if (e.target.classList.contains('color-strip') && this.selectedColorIndex !== -1) {
                        this.isPaintingColor = true;
                        this.applyColorToStrip(e.target);
                        e.preventDefault(); 
                    }
                });
                timeSection.addEventListener('mouseover', e => {
                    if (this.isPaintingColor && e.target.classList.contains('color-strip') && this.selectedColorIndex !== -1) {
                        this.applyColorToStrip(e.target);
                    }
                });
                window.addEventListener('mouseup', () => {
                    if (this.isPaintingColor) {
                        this.isPaintingColor = false;
                    }
                });

                document.getElementById('note1').addEventListener('input', () => this.saveGlobalNotes());
                document.getElementById('note2').addEventListener('input', () => this.saveGlobalNotes());
                document.querySelectorAll('.note-box h3').forEach(h3 => {
                    h3.addEventListener('blur', () => this.saveGlobalNotes());
                });
                
                document.querySelectorAll('.modal .close').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const modalId = e.target.dataset.modalId;
                        if(modalId) {
                            document.getElementById(modalId).style.display = 'none';
                            if (modalId === 'arrangeTaskModal') this.editingTaskIndex = null;
                        }
                    });
                });
                window.addEventListener('click', e => {
                    if (e.target.classList.contains('modal')) {
                        e.target.style.display = 'none';
                        if (e.target.id === 'arrangeTaskModal') this.editingTaskIndex = null;
                    }
                     if (!e.target.closest('.time-label') && !e.target.closest('.context-menu') && !e.target.closest('.color-panel-section')) { 
                         this.hideContextMenu();
                    }
                });

                document.getElementById('timeLabelContextMenu').addEventListener('click', e => {
                    const actionTarget = e.target.closest('li[data-action]');
                    if (actionTarget) this.handleContextMenuAction(actionTarget.dataset.action);
                });
                document.getElementById('taskListInModal').addEventListener('click', e => this.handleTaskListInteraction(e));
            }
            
            handleColorStripClick(stripElement) {
                if (this.selectedColorIndex === -1 || this.selectedColorIndex >= this.data.colorPalette.length) return;
                this.applyColorToStrip(stripElement);
            }

            applyColorToStrip(stripElement) {
                const hour = stripElement.dataset.hour;
                const selectedPaletteItem = this.data.colorPalette[this.selectedColorIndex];
                const newColorToApply = selectedPaletteItem.color;
                stripElement.style.backgroundColor = newColorToApply;
                this.saveTask(hour, null, null, newColorToApply);
            }

            getDayTaskData(hourStr) {
                const hour = parseInt(hourStr);
                const dateKey = this.formatDate(this.currentDate);
                if (this.data[dateKey] && this.data[dateKey].tasks && this.data[dateKey].tasks[hour]) {
                    return this.data[dateKey].tasks[hour];
                }
                return null; 
            }

            showContextMenu(targetElement) {
                const menu = document.getElementById('timeLabelContextMenu');
                const timeSection = document.getElementById('timeSection');
                const targetRect = targetElement.getBoundingClientRect();
                const timeSectionRect = timeSection.getBoundingClientRect();
                menu.style.top = `${targetRect.bottom - timeSectionRect.top + timeSection.scrollTop}px`;
                menu.style.left = `${targetRect.left - timeSectionRect.left + timeSection.scrollLeft}px`;
                menu.style.display = 'block';
            }
            hideContextMenu() { 
                const menu = document.getElementById('timeLabelContextMenu');
                if (menu) menu.style.display = 'none';
            }

            handleContextMenuAction(action) {
                if (this.contextMenuHour === null && action !== 'arrange') return; 
                const hour = this.contextMenuHour;
                const taskInput = document.querySelector(`.task-input[data-hour="${hour}"]`);

                switch(action) {
                    case 'clear':
                        if (taskInput) taskInput.value = '';
                        this.saveTask(hour, '', null, null); 
                        break;
                    case 'split':
                        if (taskInput) {
                            const originalValue = taskInput.value;
                            const newValue = ' | ' + originalValue;
                            taskInput.value = newValue;
                            taskInput.focus();
                            taskInput.setSelectionRange(0, 0);
                            this.saveTask(hour, newValue, null, null);
                        }
                        break;
                    case 'collect':
                        if (taskInput && taskInput.value.trim() !== '') {
                            const newTaskText = taskInput.value.trim();
                            const isDuplicate = this.data.taskList.some(task => task.text.toLowerCase() === newTaskText.toLowerCase());

                            if (isDuplicate) {
                                alert('任务列表中已存在相同文字的任务，仅清空当前条目。');
                            } else {
                                this.data.taskList.push({ text: newTaskText, category: '未分类', isRecurring: false });
                                this.saveData();
                                alert('任务已收纳到列表！');
                                if (document.getElementById('arrangeTaskModal').style.display === 'flex') {
                                   this.renderTaskListInModal();
                                }
                            }
                            taskInput.value = '';
                            this.saveTask(hour, '', null, this.getDayTaskData(hour)?.color || this.data.colorPalette[0].color);

                        } else {
                            alert('没有可收纳的任务内容。');
                        }
                        break;
                    case 'arrange':
                        this.showArrangeTaskModal();
                        break;
                }
                if (action !== 'arrange') this.hideContextMenu(); 
            }
            
            showArrangeTaskModal() {
                this.editingTaskIndex = null; 
                this.renderTaskListInModal();
                document.getElementById('arrangeTaskModal').style.display = 'flex';
                this.hideContextMenu(); 
            }

            renderTaskListInModal() {
                const container = document.getElementById('taskListInModal');
                const h3 = container.querySelector('h3');
                container.innerHTML = ''; 
                if (h3) container.appendChild(h3);

                if (!this.data.taskList || this.data.taskList.length === 0) {
                    container.insertAdjacentHTML('beforeend', '<p>任务列表为空。</p>');
                    return;
                }

                const groupedTasks = this.data.taskList.reduce((acc, task, index) => {
                    task.originalIndex = index; 
                    const category = task.category || '未分类';
                    if (!acc[category]) acc[category] = [];
                    acc[category].push(task);
                    return acc;
                }, {});
                
                const sortedCategories = Object.keys(groupedTasks).sort((a, b) => {
                    if (a === '未分类') return -1; if (b === '未分类') return 1;
                    return a.localeCompare(b);
                });

                for (const category of sortedCategories) {
                    const groupDiv = document.createElement('div');
                    groupDiv.className = 'task-category-group';
                    groupDiv.innerHTML = `<h4>${category} (${groupedTasks[category].length})</h4>`;
                    const ul = document.createElement('ul');
                    ul.style.listStyleType = 'none';

                    groupedTasks[category].forEach(task => {
                        const li = document.createElement('li');
                        li.className = 'task-item';
                        li.dataset.index = task.originalIndex; 

                        if (this.editingTaskIndex === task.originalIndex) {
                            li.classList.add('editing');
                            li.innerHTML = `
                                <div class="edit-task-form">
                                    <label>任务内容:</label>
                                    <input type="text" class="edit-task-text" value="${this.escapeHtml(task.text)}">
                                    <label>分类:</label>
                                    <input type="text" class="edit-task-category" value="${this.escapeHtml(task.category)}">
                                    <label>
                                        <input type="checkbox" class="edit-task-recurring" ${task.isRecurring ? 'checked' : ''}>
                                        日常任务
                                    </label>
                                    <div class="edit-actions">
                                        <button class="btn btn-save-edit-task" data-index="${task.originalIndex}">保存</button>
                                        <button class="btn btn-cancel-edit-task" data-index="${task.originalIndex}" style="background-color:#6c757d;">取消</button>
                                    </div>
                                </div>`;
                        } else {
                            li.innerHTML = `
                                <div class="task-item-main">
                                    <span class="task-item-details">
                                        ${this.escapeHtml(task.text)}
                                        ${task.isRecurring ? '<span class="is-recurring-badge">[日常]</span>' : ''}
                                    </span>
                                    <span class="task-item-actions-toggle">
                                        <button class="btn-append-task" data-index="${task.originalIndex}" style="background-color: #17a2b8; margin-right: 5px;">追加</button>
                                        <button class="btn-select-task" data-index="${task.originalIndex}" style="background-color: #007bff; margin-right: 5px;">选定</button>
                                        <button class="btn-task-settings" data-index="${task.originalIndex}">设置</button>
                                    </span>
                                </div>
                                <div class="task-item-hidden-actions">
                                    <button class="btn-edit-task" data-index="${task.originalIndex}">编辑</button>
                                    <button class="btn-toggle-recurring" data-index="${task.originalIndex}">${task.isRecurring ? '取消日常' : '设为日常'}</button>
                                    <button class="btn-delete-task" data-index="${task.originalIndex}">删除</button>
                                </div>`;
                        }
                        ul.appendChild(li);
                    });
                    groupDiv.appendChild(ul);
                    container.appendChild(groupDiv);
                }
            }
            escapeHtml(unsafe) {
                if (typeof unsafe !== 'string') return unsafe;
                return unsafe
                     .replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;")
                     .replace(/"/g, "&quot;").replace(/'/g, "&#039;");
            }

            handleTaskListInteraction(event) {
                const target = event.target;
                const taskItemElement = target.closest('.task-item');
                let taskIndex = taskItemElement ? parseInt(taskItemElement.dataset.index) : parseInt(target.dataset.index);
                const isValidTaskIndex = !isNaN(taskIndex) && taskIndex >= 0 && taskIndex < this.data.taskList.length;

                if (target.classList.contains('btn-task-settings') && isValidTaskIndex) {
                    const hiddenActions = taskItemElement.querySelector('.task-item-hidden-actions');
                    if (hiddenActions) hiddenActions.classList.toggle('visible');
                
                } else if (target.classList.contains('btn-edit-task') && isValidTaskIndex) {
                    this.editingTaskIndex = taskIndex;
                    this.renderTaskListInModal(); 
                
                } else if (target.classList.contains('btn-save-edit-task')) {
                    if (this.editingTaskIndex === null || this.editingTaskIndex >= this.data.taskList.length) return;
                    const itemBeingEdited = this.data.taskList[this.editingTaskIndex];
                    const li = target.closest('li.task-item.editing');
                    itemBeingEdited.text = li.querySelector('.edit-task-text').value.trim();
                    itemBeingEdited.category = li.querySelector('.edit-task-category').value.trim() || '未分类';
                    itemBeingEdited.isRecurring = li.querySelector('.edit-task-recurring').checked;
                    this.editingTaskIndex = null;
                    this.saveData(); this.renderTaskListInModal();

                } else if (target.classList.contains('btn-cancel-edit-task')) {
                    this.editingTaskIndex = null; this.renderTaskListInModal();

                } else if (target.classList.contains('btn-toggle-recurring') && isValidTaskIndex) {
                    this.data.taskList[taskIndex].isRecurring = !this.data.taskList[taskIndex].isRecurring;
                    this.saveData(); this.renderTaskListInModal(); 
                
                } else if (target.classList.contains('btn-delete-task') && isValidTaskIndex) {
                    if (confirm(`确定要删除任务 "${this.data.taskList[taskIndex].text}" 吗?`)) {
                        this.data.taskList.splice(taskIndex, 1);
                        if (this.editingTaskIndex !== null) {
                            if (this.editingTaskIndex === taskIndex) this.editingTaskIndex = null;
                            else if (this.editingTaskIndex > taskIndex) this.editingTaskIndex--;
                        }
                        this.saveData(); this.renderTaskListInModal();
                    }
                } else if (target.classList.contains('btn-append-task') && isValidTaskIndex) {
                    if (this.contextMenuHour === null) {
                        alert("请先通过右键时间标签选择要安排的时间点。");
                        return;
                    }
                    const taskToAppend = this.data.taskList[taskIndex];
                    const taskInput = document.querySelector(`.task-input[data-hour="${this.contextMenuHour}"]`);

                    if (taskInput) {
                        const originalValue = taskInput.value;
                        const newValue = taskToAppend.text + ' | ' + originalValue;
                        taskInput.value = newValue;

                        const existingColor = this.getDayTaskData(this.contextMenuHour)?.color || this.data.colorPalette[0].color;
                        this.saveTask(this.contextMenuHour, newValue, false, existingColor);

                        taskInput.focus();
                        taskInput.setSelectionRange(0, 0);
                    }

                    if (!taskToAppend.isRecurring) {
                        this.data.taskList.splice(taskIndex, 1);
                        if (this.editingTaskIndex !== null) {
                            if (this.editingTaskIndex === taskIndex) this.editingTaskIndex = null;
                            else if (this.editingTaskIndex > taskIndex) this.editingTaskIndex--;
                        }
                        this.saveData();
                        this.renderTaskListInModal();
                    } else {
                        this.saveData();
                    }
                    
                    if (taskInput) {
                        document.getElementById('arrangeTaskModal').style.display = 'none';
                    }
                } else if (target.classList.contains('btn-select-task') && isValidTaskIndex) {
                     if (this.contextMenuHour === null) {
                        alert("请先通过右键时间标签选择要安排的时间点。"); return;
                    }
                    const taskToSchedule = this.data.taskList[taskIndex];
                    const taskInput = document.querySelector(`.task-input[data-hour="${this.contextMenuHour}"]`);
                    if (taskInput) {
                        taskInput.value = taskToSchedule.text;
                        const existingColor = this.getDayTaskData(this.contextMenuHour)?.color || this.data.colorPalette[0].color;
                        this.saveTask(this.contextMenuHour, taskToSchedule.text, false, existingColor); 
                    }
                    if (!taskToSchedule.isRecurring) {
                        this.data.taskList.splice(taskIndex, 1);
                        if (this.editingTaskIndex !== null) {
                            if (this.editingTaskIndex === taskIndex) this.editingTaskIndex = null;
                            else if (this.editingTaskIndex > taskIndex) this.editingTaskIndex--;
                        }
                        this.saveData(); this.renderTaskListInModal(); 
                    } else {
                        this.saveData(); 
                    }
                    if (taskInput) document.getElementById('arrangeTaskModal').style.display = 'none'; 
                }
            }

            setDayScore(score) { 
                if (!this.canEdit()) return;
                const dateKey = this.formatDate(this.currentDate);
                this.initDayData(dateKey);
                this.data[dateKey].dayScore = (this.data[dateKey].dayScore === score) ? 'none' : score;
                this.saveData(); this.updateScoreButtons();
            }

            updateScoreButtons() {
                const canEditToday = this.canEdit();
                const completeBtn = document.getElementById('completeBtn');
                const halfBtn = document.getElementById('halfBtn');
                completeBtn.classList.toggle('hidden', !canEditToday);
                halfBtn.classList.toggle('hidden', !canEditToday);

                if (canEditToday) {
                    const dateKey = this.formatDate(this.currentDate);
                    const dayData = this.data[dateKey];
                    completeBtn.disabled = false; halfBtn.disabled = false;
                    completeBtn.classList.remove('inactive'); halfBtn.classList.remove('inactive');
                    
                    if (dayData && dayData.dayScore === 'complete') {
                        halfBtn.classList.add('inactive');
                    } else if (dayData && dayData.dayScore === 'half') {
                        completeBtn.classList.add('inactive');
                    } else {
                        completeBtn.classList.add('inactive');
                        halfBtn.classList.add('inactive');
                    }
                } else {
                    completeBtn.disabled = true; halfBtn.disabled = true;
                }
            }

            canEdit() { 
                const today = new Date(); today.setHours(0,0,0,0);
                const yesterday = new Date(today); yesterday.setDate(yesterday.getDate() - 1);
                return this.currentDate.getTime() === today.getTime() || this.currentDate.getTime() === yesterday.getTime();
            }
            
            initDayData(dateKey) {
                if (!this.data[dateKey]) this.data[dateKey] = { tasks: {}, dayScore: 'none', dailyNote: '' };
                if (!this.data[dateKey].tasks) this.data[dateKey].tasks = {}; 
                if (typeof this.data[dateKey].dailyNote === 'undefined') this.data[dateKey].dailyNote = '';
            }

            saveTask(hourStr, text, completed, color) {
                const hour = parseInt(hourStr); 
                const dateKey = this.formatDate(this.currentDate);
                this.initDayData(dateKey);
                if (!this.data[dateKey].tasks[hour]) {
                    this.data[dateKey].tasks[hour] = { text: '', completed: false, color: this.data.colorPalette[0].color };
                }
                if (text !== null) this.data[dateKey].tasks[hour].text = text;
                if (completed !== null) this.data[dateKey].tasks[hour].completed = completed;
                if (color !== null) this.data[dateKey].tasks[hour].color = color;
                this.saveData();
            }

            saveDailyNote() {
                const dateKey = this.formatDate(this.currentDate);
                this.initDayData(dateKey);
                const noteInput = document.getElementById('dailyNoteInput');
                if (noteInput) {
                    this.data[dateKey].dailyNote = noteInput.value;
                    this.saveData();
                }
            }

            saveGlobalNotes() {
                if (!this.data.globalNotes) this.data.globalNotes = {};
                this.data.globalNotes.title1 = document.querySelector('.note-box:nth-child(1) h3').textContent;
                this.data.globalNotes.note1 = document.getElementById('note1').value;
                this.data.globalNotes.title2 = document.querySelector('.note-box:nth-child(2) h3').textContent;
                this.data.globalNotes.note2 = document.getElementById('note2').value;
                this.saveData();
            }
            loadGlobalNotes() {
                const notes = this.data.globalNotes || { note1: '', note2: '', title1: '文本框1', title2: '文本框2' };
                document.querySelector('.note-box:nth-child(1) h3').textContent = notes.title1 || '文本框1';
                document.getElementById('note1').value = notes.note1 || '';
                document.querySelector('.note-box:nth-child(2) h3').textContent = notes.title2 || '文本框2';
                document.getElementById('note2').value = notes.note2 || '';
            }

            loadPageData() {
                const dateKey = this.formatDate(this.currentDate); 
                const dayData = this.data[dateKey];
                
                const dailyNoteInput = document.getElementById('dailyNoteInput');
                if (dailyNoteInput) {
                    dailyNoteInput.value = (dayData && dayData.dailyNote) ? dayData.dailyNote : '';
                }

                document.querySelectorAll('.task-input:not(#dailyNoteInput)').forEach(input => { input.value = ''; input.classList.remove('completed');});
                document.querySelectorAll('.task-checkbox').forEach(checkbox => checkbox.checked = false);
                document.querySelectorAll('.color-strip').forEach(strip => strip.style.backgroundColor = this.data.colorPalette[0].color); 

                if (dayData && dayData.tasks) { 
                    for (let hour = 0; hour < 24; hour++) {
                        const taskInput = document.querySelector(`input.task-input[data-hour="${hour}"]`);
                        const taskCheckbox = document.querySelector(`input.task-checkbox[data-hour="${hour}"]`);
                        const colorStrip = document.querySelector(`.color-strip[data-hour="${hour}"]`);
                        const taskData = dayData.tasks[hour];
                        if (taskInput && taskData) { 
                            taskInput.value = taskData.text || '';
                            taskInput.classList.toggle('completed', !!taskData.completed);
                            if(taskCheckbox) taskCheckbox.checked = !!taskData.completed;
                            if(colorStrip) {
                                const colorExists = this.data.colorPalette.some(p => p.color === taskData.color);
                                colorStrip.style.backgroundColor = (taskData.color && colorExists) ? taskData.color : this.data.colorPalette[0].color;
                            }
                        } else if (colorStrip) { 
                             colorStrip.style.backgroundColor = this.data.colorPalette[0].color;
                        }
                    }
                }
                this.updateScoreButtons();
            }

            updateDisplay() { 
                document.getElementById('currentDate').textContent = this.formatDisplayDate(this.currentDate); 
                const today = new Date();
                today.setHours(0,0,0,0);
                document.getElementById('currentDate').classList.toggle('is-today', this.currentDate.getTime() === today.getTime());
                this.updateScoreButtons();
                this.updateNavButtonStates();
            }
            
            showCalendar() {
                const modal = document.getElementById('calendarModal');
                const calendarEl = document.getElementById('calendar');
                this.calendarViewDate = new Date(this.currentDate); 
                calendarEl.innerHTML = this.generateCalendarHTML(this.calendarViewDate);
                this.bindCalendarNavEvents(calendarEl); 
                modal.style.display = 'flex'; 
                
                calendarEl.onclick = (e) => { 
                    const dayElement = e.target.closest('.calendar-day');
                    if (dayElement && dayElement.dataset.date) {
                        const [year, month, day] = dayElement.dataset.date.split('-').map(Number);
                        this.currentDate = new Date(year, month - 1, day); 
                        this.currentDate.setHours(0,0,0,0);
                        this.calendarViewDate = new Date(this.currentDate);
                        this.updateDisplay();
                        this.loadPageData();
                        this.updateCurrentTime();
                        modal.style.display = 'none';
                    }
                };
            }
            bindCalendarNavEvents(calendarEl) {
                calendarEl.querySelector('button[data-action="prev-month"]').addEventListener('click', () => this.changeCalendarMonth(-1));
                calendarEl.querySelector('button[data-action="next-month"]').addEventListener('click', () => this.changeCalendarMonth(1));
                calendarEl.querySelector('button[data-action="today"]').addEventListener('click', () => this.goToTodayAndCloseCalendar());
            }
            goToTodayAndCloseCalendar() {
                this.currentDate = new Date();
                this.currentDate.setHours(0, 0, 0, 0);
                this.calendarViewDate = new Date(this.currentDate);
                this.updateDisplay();
                this.loadPageData();
                this.updateCurrentTime();
                document.getElementById('calendarModal').style.display = 'none';
            }
            generateCalendarHTML(displayDate) {
                const year = displayDate.getFullYear(); const month = displayDate.getMonth(); 
                const today = new Date(); today.setHours(0,0,0,0);

                const firstDayOfMonth = new Date(year, month, 1);
                const startDate = new Date(firstDayOfMonth); 
                startDate.setDate(startDate.getDate() - firstDayOfMonth.getDay());

                let html = `<div class="calendar-nav">
                                <button data-action="prev-month">&lt; 上一月</button>
                                <span>${year}年 ${month + 1}月</span>
                                <div>
                                    <button data-action="today" id="calendarTodayBtn">回到今日</button>
                                    <button data-action="next-month">下一月 &gt;</button>
                                </div>
                            </div>
                            <div class="calendar-grid">
                            <div>日</div><div>一</div><div>二</div><div>三</div><div>四</div><div>五</div><div>六</div>`;
                for (let i = 0; i < 42; i++) {
                    const date = new Date(startDate); 
                    date.setDate(startDate.getDate() + i);
                    
                    const dateKey = this.formatDate(date);
                    const isToday = date.getTime() === today.getTime();
                    const isCurrentDisplayMonth = date.getMonth() === month;
                    const dayData = this.data[dateKey];
                    const isSelected = date.getTime() === this.currentDate.getTime();

                    let className = 'calendar-day';
                    if (isSelected) className += ' selected'; 
                    if (isToday) className += ' today'; 
                    if (!isCurrentDisplayMonth) className += ' other-month';
                    
                    let scoreDisplay = '';
                    if (dayData && dayData.dayScore && dayData.dayScore !== 'none') {
                        scoreDisplay = `<span class="day-score">${dayData.dayScore === 'complete' ? '1' : '0.5'}</span>`;
                    }
                    let dailyNoteDisplay = '';
                    if(dayData && dayData.dailyNote) {
                        const truncatedNote = dayData.dailyNote.substring(0, 10) + (dayData.dailyNote.length > 10 ? '...' : '');
                        dailyNoteDisplay = `<span class="day-note">${this.escapeHtml(truncatedNote)}</span>`;
                    }
                    
                    html += `<div class="${className}" data-date="${this.formatDate(date)}">
                                <div class="day-number-wrapper">${date.getDate()}${scoreDisplay}</div>
                                ${dailyNoteDisplay}
                             </div>`;
                }
                html += '</div>'; return html;
            }
            changeCalendarMonth(delta) {
                this.calendarViewDate.setDate(1); 
                this.calendarViewDate.setMonth(this.calendarViewDate.getMonth() + delta);
                const calendarEl = document.getElementById('calendar');
                calendarEl.innerHTML = this.generateCalendarHTML(this.calendarViewDate);
                this.bindCalendarNavEvents(calendarEl); 
            }

            formatDate(date) { return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`; }
            formatDisplayDate(date) { return `${date.getFullYear()}.${date.getMonth() + 1}.${date.getDate()}`; }
            
            saveData() { localStorage.setItem('todoData', JSON.stringify(this.data)); }
            loadData() {
                const saved = localStorage.getItem('todoData');
                const defaultData = { 
                    globalNotes: { title1: '文本框1', title2: '文本框2', note1: '', note2: '' }, 
                    taskList: [],
                    colorPalette: [ 
                        { color: '#FFFFFF', activity: '清除' }, { color: '#FFADAD', activity: '睡眠' },
                        { color: '#FFD6A5', activity: '工作' }, { color: '#FDFFB6', activity: '学习' },
                        { color: '#CAFFBF', activity: '运动' }, { color: '#9BF6FF', activity: '休闲' },
                        { color: '#A0C4FF', activity: '用餐' }, { color: '#BDB2FF', activity: '交通' },
                        { color: '#FFC6FF', activity: '其他' }
                    ]
                };
                if (saved) {
                    const parsed = JSON.parse(saved);
                    return parsed;
                }
                return defaultData;
            }
            destroy() { if (this.currentTimeInterval) clearInterval(this.currentTimeInterval); }
        }

        let todoManager;
        document.addEventListener('DOMContentLoaded', () => {
            todoManager = new TodoManager();
            window.todoManager = todoManager; 
        });
        window.addEventListener('beforeunload', () => { if (todoManager) todoManager.destroy(); });
    </script>
</body>
</html>